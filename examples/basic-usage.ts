import { CropLibrary, plants, imagePathMap } from '../src';

async function main() {
  // Initialize the SDK
  const cropLibrary = new CropLibrary({
    language: 'en',
    cacheExpiry: 3600000, // 1 hour
    basePath: '../src/data' // Path to the images directory
  });

  try {
    // Get all crop families
    console.log('Fetching crop families...');

    const cropFamilies = await cropLibrary.getCropFamilies();
    
    console.log(`Found ${cropFamilies.length} crop families`);
    
    if (cropFamilies.length > 0) {
      // Get the first crop family's details
      const firstFamilyId = cropFamilies[0]._id.$oid;
      console.log(`Fetching details for crop family ID: ${firstFamilyId}`);
      
      const cropFamily = await cropLibrary.getCropFamilyById(firstFamilyId);
      console.log('Crop family details:', cropFamily?.name[0]?.text);
      
      // Get plants in this crop family
      console.log(`Fetching plants for crop family ID: ${firstFamilyId}`);
      const familyPlants = await cropLibrary.getPlantsByCropFamily(firstFamilyId);
      console.log(`Found ${familyPlants.length} plants in this family`);
      
      if (familyPlants.length > 0) {
        // Get details for the first plant
        const firstPlant = familyPlants[0];
        console.log('First plant:', firstPlant.langMap[0]?.name);
        
        // Get image URL if available
        if (firstPlant.profileImageId) {
          const imageUrl = cropLibrary.getImageUrl(firstPlant.profileImageId);
          console.log('Plant image URL:', imageUrl);
        }
      }
    }
    
    // Search for plants
    console.log('Searching for plants with "indigo" in the name...');
    const searchResults = await cropLibrary.searchPlants('indigo');
    console.log(`Found ${searchResults.length} plants matching the search`);
    
    // Demo of language support
    console.log('Fetching plant data in French...');
    const frenchPlants = await cropLibrary.getPlants({ language: 'fr' });
    console.log(`Found ${frenchPlants.length} plants with French data`);
    
    // Direct access to data
    console.log('\nDirect access to data:');
    console.log(`Total crop families in data: ${cropFamilies.length}`);
    console.log(`Total plants in data: ${plants.length}`);
    console.log(`Total images in data: ${Object.keys(imagePathMap).length}`);
    
  } catch (error) {
    console.error('Error:', error);
  }
}

main().catch(console.error); 