import React, { useEffect, useState } from 'react';
import { CropLibrary, CropFamily, AgriPlant } from '../src';

// Initialize the SDK - no API keys needed as all data is embedded
const cropLibrary = new CropLibrary({
  language: 'en',
  basePath: '/assets/images' // Path to the images directory in your React app
});

const CropFamilyApp = () => {
  const [loading, setLoading] = useState(true);
  const [cropFamilies, setCropFamilies] = useState<CropFamily[]>([]);
  const [selectedFamily, setSelectedFamily] = useState<CropFamily | null>(null);
  const [plants, setPlants] = useState<AgriPlant[]>([]);
  const [error, setError] = useState<string | null>(null);

  // Load crop families on component mount
  useEffect(() => {
    loadCropFamilies();
  }, []);

  // Load plants when a family is selected
  useEffect(() => {
    if (selectedFamily) {
      loadPlantsByFamily(selectedFamily._id.$oid);
    }
  }, [selectedFamily]);

  const loadCropFamilies = async () => {
    try {
      setLoading(true);
      const families = await cropLibrary.getCropFamilies();
      setCropFamilies(families);
      setError(null);
    } catch (err) {
      setError('Failed to load crop families');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const loadPlantsByFamily = async (familyId: string) => {
    try {
      setLoading(true);
      const plantsList = await cropLibrary.getPlantsByCropFamily(familyId);
      setPlants(plantsList);
      setError(null);
    } catch (err) {
      setError('Failed to load plants');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleFamilySelect = (family: CropFamily) => {
    setSelectedFamily(family);
  };

  const handleBack = () => {
    setSelectedFamily(null);
    setPlants([]);
  };

  if (loading && !cropFamilies.length && !plants.length) {
    return (
      <div className="loading-container">
        <div className="spinner"></div>
        <p>Loading...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="error-container">
        <p className="error-text">{error}</p>
        <button className="button" onClick={loadCropFamilies}>Retry</button>
      </div>
    );
  }

  return (
    <div className="container">
      {selectedFamily ? (
        <div>
          <div className="header">
            <button className="back-button" onClick={handleBack}>← Back</button>
            <h2 className="header-title">
              {selectedFamily.name.find(n => n.langCode === 'en')?.text || 'Plants'}
            </h2>
          </div>
          <div className="plants-grid">
            {plants.map(plant => {
              const plantInfo = plant.langMap.find(l => l.langCode === 'en');
              const name = plantInfo?.name || 'Unknown';
              const summary = plantInfo?.summary || 'No description available';
              
              return (
                <div className="plant-card" key={plant._id.$oid}>
                  {plant.profileImageId && (
                    <img 
                      src={cropLibrary.getImageUrl(plant.profileImageId) || undefined} 
                      alt={name}
                      className="plant-image"
                    />
                  )}
                  <div className="plant-info">
                    <h3 className="plant-title">{name}</h3>
                    <p className="plant-summary">{summary}</p>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      ) : (
        <div>
          <div className="header">
            <h2 className="header-title">Crop Families</h2>
          </div>
          <div className="families-grid">
            {cropFamilies.map(family => {
              const name = family.name.find(n => n.langCode === 'en')?.text || 'Unknown';
              
              return (
                <div 
                  className="family-card" 
                  key={family._id.$oid}
                  onClick={() => handleFamilySelect(family)}
                >
                  <h3 className="family-title">{name}</h3>
                  {family.imageId && (
                    <img 
                      src={cropLibrary.getImageUrl(family.imageId) || undefined} 
                      alt={name}
                      className="family-image"
                    />
                  )}
                </div>
              );
            })}
          </div>
        </div>
      )}
      
      <style jsx>{`
        .container {
          max-width: 1200px;
          margin: 0 auto;
          padding: 20px;
          font-family: Arial, sans-serif;
        }
        
        .loading-container, .error-container {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 300px;
        }
        
        .spinner {
          border: 4px solid #f3f3f3;
          border-top: 4px solid #3498db;
          border-radius: 50%;
          width: 40px;
          height: 40px;
          animation: spin 1s linear infinite;
          margin-bottom: 20px;
        }
        
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
        
        .error-text {
          color: red;
          margin-bottom: 20px;
        }
        
        .header {
          display: flex;
          align-items: center;
          padding: 15px 0;
          border-bottom: 1px solid #e0e0e0;
          margin-bottom: 20px;
        }
        
        .header-title {
          flex: 1;
          text-align: center;
          margin: 0;
        }
        
        .back-button {
          background: none;
          border: none;
          color: #007bff;
          font-size: 16px;
          cursor: pointer;
        }
        
        .families-grid, .plants-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
          gap: 20px;
        }
        
        .family-card, .plant-card {
          background-color: #fff;
          border-radius: 8px;
          box-shadow: 0 2px 4px rgba(0,0,0,0.1);
          overflow: hidden;
          cursor: pointer;
          transition: transform 0.2s;
        }
        
        .family-card:hover {
          transform: translateY(-5px);
        }
        
        .family-title, .plant-title {
          padding: 15px;
          margin: 0;
        }
        
        .family-image, .plant-image {
          width: 100%;
          height: 150px;
          object-fit: cover;
        }
        
        .plant-info {
          padding: 0 15px 15px;
        }
        
        .plant-summary {
          color: #666;
          margin-top: 5px;
        }
        
        .button {
          background-color: #007bff;
          color: white;
          border: none;
          padding: 10px 20px;
          border-radius: 5px;
          cursor: pointer;
        }
      `}</style>
    </div>
  );
};

export default CropFamilyApp; 