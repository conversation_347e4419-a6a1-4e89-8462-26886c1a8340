import { DiseaseLibrary, diseases, diseaseTypes, imagePathMap } from '../src';

async function main() {
  // Initialize the Disease Library SDK
  const diseaseLibrary = new DiseaseLibrary({
    language: 'en',
    cacheExpiry: 3600000, // 1 hour
    basePath: '../src/data' // Path to the images directory
  });

  try {
    // Get all disease types
    console.log('Fetching disease types...');
    const diseaseTypes = await diseaseLibrary.getDiseaseTypes();
    console.log(`Found ${diseaseTypes.length} disease types`);
    
    // Display first few disease types
    diseaseTypes.slice(0, 3).forEach(diseaseType => {
      const name = diseaseType.name.find(n => n.langCode === 'en')?.text || 'Unknown';
      console.log(`- Disease Type: ${name} (ID: ${diseaseType._id.$oid})`);
    });

    // Get all diseases
    console.log('\nFetching diseases...');
    const diseases = await diseaseLibrary.getDiseases();
    console.log(`Found ${diseases.length} diseases`);
    
    // Display first few diseases
    diseases.slice(0, 5).forEach(disease => {
      const name = disease.langMap.find(lang => lang.langCode === 'en')?.name || 'Unknown';
      console.log(`- Disease: ${name} (ID: ${disease._id.$oid})`);
    });

    // Get diseases by type (using the first disease type)
    if (diseaseTypes.length > 0) {
      const firstDiseaseType = diseaseTypes[0];
      const diseaseTypeName = firstDiseaseType.name.find(n => n.langCode === 'en')?.text || 'Unknown';
      
      console.log(`\nFetching diseases of type: ${diseaseTypeName}...`);
      const diseasesByType = await diseaseLibrary.getDiseasesByType(firstDiseaseType._id.$oid);
      console.log(`Found ${diseasesByType.length} diseases of type ${diseaseTypeName}`);
      
      // Display first few diseases of this type
      diseasesByType.slice(0, 3).forEach(disease => {
        const name = disease.langMap.find(lang => lang.langCode === 'en')?.name || 'Unknown';
        console.log(`  - ${name}`);
      });
    }

    // Search for diseases
    console.log('\nSearching for diseases containing "blight"...');
    const searchResults = await diseaseLibrary.searchDiseases('blight');
    console.log(`Found ${searchResults.length} diseases matching "blight"`);
    
    searchResults.slice(0, 3).forEach(disease => {
      const name = disease.langMap.find(lang => lang.langCode === 'en')?.name || 'Unknown';
      console.log(`  - ${name}`);
    });

    // Get a specific disease by ID (using the first disease)
    if (diseases.length > 0) {
      const firstDisease = diseases[0];
      const diseaseById = await diseaseLibrary.getDiseaseById(firstDisease._id.$oid);
      if (diseaseById) {
        const name = diseaseById.langMap.find(lang => lang.langCode === 'en')?.name || 'Unknown';
        console.log(`\nRetrieved disease by ID: ${name}`);
        
        // Get image URL if available
        if (diseaseById.profileImageId) {
          const imageUrl = diseaseLibrary.getImageUrl(diseaseById.profileImageId);
          console.log(`  Image URL: ${imageUrl || 'No image available'}`);
        }
      }
    }

    // Get a specific disease type by ID (using the first disease type)
    if (diseaseTypes.length > 0) {
      const firstDiseaseType = diseaseTypes[0];
      const diseaseTypeById = await diseaseLibrary.getDiseaseTypeById(firstDiseaseType._id.$oid);
      if (diseaseTypeById) {
        const name = diseaseTypeById.name.find(n => n.langCode === 'en')?.text || 'Unknown';
        const description = diseaseTypeById.description.find(d => d.langCode === 'en')?.text || 'No description';
        console.log(`\nRetrieved disease type by ID: ${name}`);
        console.log(`  Description: ${description.substring(0, 100)}...`);
        
        // Get image URL if available
        if (diseaseTypeById.imageId) {
          const imageUrl = diseaseLibrary.getImageUrl(diseaseTypeById.imageId);
          console.log(`  Image URL: ${imageUrl || 'No image available'}`);
        }
      }
    }

    // Show some statistics
    console.log('\n--- Disease Library Statistics ---');
    console.log(`Total disease types: ${diseaseTypes.length}`);
    console.log(`Total diseases: ${diseases.length}`);
    
    // Count diseases by type
    const diseaseCountByType: Record<string, number> = {};
    diseases.forEach(disease => {
      const typeId = disease.diseaseTypeId.$oid;
      diseaseCountByType[typeId] = (diseaseCountByType[typeId] || 0) + 1;
    });
    
    console.log('\nDiseases by type:');
    Object.entries(diseaseCountByType).forEach(([typeId, count]) => {
      const diseaseType = diseaseTypes.find(dt => dt._id.$oid === typeId);
      const typeName = diseaseType?.name.find(n => n.langCode === 'en')?.text || 'Unknown';
      console.log(`  ${typeName}: ${count} diseases`);
    });

    // Show available image paths
    const diseaseImagePaths = Object.values(imagePathMap).filter(path => 
      path.includes('/DISEASES/') || path.includes('/DISEASE_TYPE/')
    );
    console.log(`\nTotal disease-related images available: ${diseaseImagePaths.length}`);

  } catch (error) {
    console.error('Error using Disease Library:', error);
  }
}

// Run the example
main().catch(console.error);
