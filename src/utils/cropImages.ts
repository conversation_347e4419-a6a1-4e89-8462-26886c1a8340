// Direct crop image mapping using require()
const imagePlaceholder = require('../../../assets/img/smallImage.png');

// Map crop names to their require() statements
const cropImageMap: Record<string, any> = {
  // Existing mappings
  'MAIZE': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/MAIZE.png'),
  'RUTABAGA': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/RUTABAGA.png'),
  'WHEAT': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/WHEAT.png'),
  'RYE': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/RYE.png'),
  'OAT': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/OAT.png'),
  'PINEAPPLE': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/PINEAPPLE.png'),
  'MELON': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/MELON.png'),
  'KOHLRABI': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/KOHLRABI.png'),
  'LABLAB_BEAN': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/LABLAB_BEAN.png'),
  'KOLA_NUT': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/KOLA_NUT.png'),
  'CARROT': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/CARROT.png'),
  'RED_CLOVER': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/RED_CLOVER.png'),
  'KIDNEY_BEAN': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/KIDNEY_BEAN.png'),
  'PEANUT': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/PEANUT.png'),
  'GLIRICIDIA': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/GLIRICIDIA.png'),
  'CORN': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/CORN.png'),
  'APPLE': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/APPLE.png'),
  'COCOA': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/COCOA.png'),
  'INDIGO_AFRICAN': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/INDIGO_AFRICAN.png'),
  'WATERCRESS': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/WATERCRESS.png'),
  'CHIVE': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/CHIVE.png'),
  'BOK_CHOY': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/BOK_CHOY.png'),
  'ORANGE': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/ORANGE.png'),
  'CHINESE_CABBAGE': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/CHINESE_CABBAGE.png'),
  'BARLEY': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/BARLEY.png'),
  'PEAR': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/PEAR.png'),
  'BROCCOLI': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/BROCCOLI.png'),
  'FENNEL': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/FENNEL.png'),
  'LEEK': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/LEEK.png'),
  'CUSTARD_FRUIT': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/CUSTARD_FRUIT.png'),
  'MANGO': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/MANGO.png'),
  'OIL_PALM': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/OIL_PALM.png'),
  'CHAYOTE': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/CHAYOTE.png'),
  'PARSNIP': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/PARSNIP.png'),
  'LEMON': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/LEMON.png'),
  'OKRO': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/OKRO.png'),
  'COFFEE': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/COFFEE.png'),
  'AUBERGINE': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/AUBERGINE.png'),
  'BOTTLE_GOURD': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/BOTTLE_GOURD.png'),
  'SHALLOT': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/SHALLOT.png'),
  'BITTER_GOURD': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/BITTER_GOURD.png'),
  'BRUSSELS_SPROUTS': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/BRUSSELS_SPROUTS.png'),
  'IVY_GOURD': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/IVY_GOURD.png'),
  'ENDIVE': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/ENDIVE.png'),
  'CASSAVA': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/CASSAVA.png'),
  'SNAKE_GOURD': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/SNAKE_GOURD.png'),
  'POTATO': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/POTATO.png'),
  'PLANTAIN': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/PLANTAIN.png'),
  'CASHEW': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/CASHEW.png'),
  'WATERMELON': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/WATERMELON.png'),
  'COTTON': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/COTTON.png'),
  'PAWPAW': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/PAWPAW.png'),
  'RADISH': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/RADISH.png'),
  'JUTE': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/JUTE.png'),
  'BLACK_BEAN': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/BLACK_BEAN.png'),
  'DILL': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/DILL.png'),
  'INDIGO': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/INDIGO.png'),
  'SUNFLOWER': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/SUNFLOWER.png'),
  'CORIANDER': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/CORIANDER.png'),
  'STRAWBERRY': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/STRAWBERRY.png'),
  'LETTUCE': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/LETTUCE.png'),
  'KALE': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/KALE.png'),
  'GROUNDNUT': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/GROUNDNUT.png'),
  'SWEET_POTATO': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/SWEET_POTATO.png'),
  'MUSTARD': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/MUSTARD.png'),
  'DWARF_BEAN': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/DWARF_BEAN.png'),
  'MARIGOLD': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/MARIGOLD.png'),
  'CAULIFLOWER': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/CAULIFLOWER.png'),
  'RUNNER_BEAN': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/RUNNER_BEAN.png'),
  'HYACINTH_BEAN': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/HYACINTH_BEAN.png'),
  'CUMIN': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/CUMIN.png'),
  'IRISH_POTATO': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/IRISH_POTATO.png'),
  'TARO': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/TARO.png'),
  'DESMODIUM': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/DESMODIUM.png'),
  'GARLIC': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/GARLIC.png'),
  'MUNGBEAN': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/MUNGBEAN.png'),
  'JERUSALEM_ARTICHOKE': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/JERUSALEM_ARTICHOKE.png'),
  'CLIMBING_BEAN': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/CLIMBING_BEAN.png'),
  'PARSLEY': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/PARSLEY.png'),
  'CABBAGE': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/CABBAGE.png'),
  'UGU': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/UGU.png'),
  'ONION': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/ONION.png'),
  'MOGUL_BARREL_MEDIC': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/MOGUL_BARREL_MEDIC.png'),
  'MACADAMIA_NUT': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/MACADAMIA_NUT.png'),
  'SIRATRO': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/SIRATRO.png'),
  'COLLARD': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/COLLARD.png'),
  'WHITE_BEAN': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/WHITE_BEAN.png'),
  'OKRA': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/OKRA.png'),
  'BANANA_AND_PLANTAIN': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/BANANA_AND_PLANTAIN.png'),
  'GINGER': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/GINGER.png'),
  'TURNIP': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/TURNIP.png'),
  'COWPEA': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/COWPEA.png'),
  'EGGPLANT': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/EGGPLANT.png'),
  'PIGEON_PEA': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/PIGEON_PEA.png'),
  'PUMPKINS': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/PUMPKINS.png'),
  'TOMATO': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/TOMATO.png'),
  'CHERRY': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/CHERRY.png'),
  'ARTICHOKE': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/ARTICHOKE.png'),
  'MILLET': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/MILLET.png'),
  'CUCUMBER': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/CUCUMBER.png'),
  'COURGETTE': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/COURGETTE.png'),
  'LENTIL': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/LENTIL.png'),
  'FRENCH_BEAN': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/FRENCH_BEAN.png'),
  'RICE': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/RICE.png'),
  'YAM': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/YAM.png'),
  'ALFALFA': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/ALFALFA.png'),
  'CELERY': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/CELERY.png'),
  'OILSEED_RADISH': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/OILSEED_RADISH.png'),
  'SALAD': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/SALAD.png'),
  'BROAD_BEANS': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/BROAD_BEANS.png'),
  'SORGHUM': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/SORGHUM.png'),
  'GRAPE': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/GRAPE.png'),
  'GLIRICIDIA_TREE': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/GLIRICIDIA_TREE.png'),
  
  // New mappings for crops from logs
  'BANANA': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/BANANA_AND_PLANTAIN.png'),
  'BROAD_BEAN': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/BROAD_BEANS.png'),
  'BRUSSELS_SPROUT': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/BRUSSELS_SPROUTS.png'),
  'COMMON_BEAN': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/FRENCH_BEAN.png'),
  'DASHEEN': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/TARO.png'),
  'GARBANZO': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/CHICKPEA.png'),
  'GUINEA_CORN': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/SORGHUM.png'),
  'KOHL_RABI': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/KOHLRABI.png'),
  'LIMA_BEAN': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/FRENCH_BEAN.png'),
  'LUPIN': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/PEANUT.png'),
  'PECAN_NUT': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/MACADAMIA_NUT.png'),
  'PEPPER_CHILLI': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/CHILLI_PEPPER.png'),
  'PISTACHIO_NUT': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/MACADAMIA_NUT.png'),
  'RAPE': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/MUSTARD.png'),
  'RASPBERRY': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/RASPBERRY.png'),
  'SAVA_SNAIL_MEDIC': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/MOGUL_BARREL_MEDIC.png'),
  'SNAP_PEA': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/SUGAR_PEA.png'),
  'SNOW_PEA': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/SUGAR_PEA.png'),
  'SOYBEAN': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/FRENCH_BEAN.png'),
  'SQUASH': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/PUMPKINS.png'),
  'STEVIA': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/SWEET_PEPPER.png'),
  'STRING_BEAN': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/FRENCH_BEAN.png'),
  'SWEDE': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/RUTABAGA.png'),
  'TOBACCO': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/TOMATO.png'),
  'WATER_CHESTNUT': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/WATERCRESS.png'),
  'WAX_GOURD': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/BOTTLE_GOURD.png'),
  'WOAD': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/INDIGO.png'),
  
  // Additional mappings for common variations
  'CHICKPEA': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/CHICKPEA.png'),
  'CHILLI_PEPPER': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/CHILLI_PEPPER.png'),
  'CHIVES': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/CHIVE.png'),
  'ENDIVE_CHICORY': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/ENDIVE.png'),
  'FLUTED_PUMPKIN_(UGU)': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/FLUTED_PUMPKIN.png'),
  'RICE_OUTLINE': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/RICE.png'),
  'SIRATRO_BEAN': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/SIRATRO.png'),
  'SUGAR_PEA': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/SUGAR_PEA.png'),
  'TARO_DASHEEN': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/TARO.png'),
  'WHEAT_OUTLINE': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/WHEAT.png'),
  
  // Alternative spellings and variations
  'BOK_CHOI': require('../data/images/AGRI_DATA/AGRI_PLANTS/PNG/BOK_CHOY.png'),
};

/**
 * Get crop image by crop name
 * @param cropName - The name of the crop
 * @returns The image source for the crop or a placeholder if not found
 */
export const getCropImage = (cropName: string) => {
  // console.log(cropName);
  if (!cropName) return imagePlaceholder;
  
  // Normalize the crop name: uppercase and replace spaces with underscores
  const normalizedName = cropName.toUpperCase().trim().replace(/\s+/g, '_');
  
  // Try exact match first
  if (cropImageMap[normalizedName]) {
    return cropImageMap[normalizedName];
  }
  
  // Try partial matches with improved logic
  for (const [key, image] of Object.entries(cropImageMap)) {
    // Check if the normalized name contains the key or vice versa
    if (normalizedName.includes(key) || key.includes(normalizedName)) {
      return image;
    }
    
    // Also try matching without underscores
    const nameWithoutUnderscores = normalizedName.replace(/_/g, '');
    const keyWithoutUnderscores = key.replace(/_/g, '');
    if (nameWithoutUnderscores.includes(keyWithoutUnderscores) || keyWithoutUnderscores.includes(nameWithoutUnderscores)) {
      return image;
    }
  }
  
  // Fallback to placeholder
  return imagePlaceholder;
};

/**
 * Get all available crop names that have images
 * @returns Array of crop names that have corresponding images
 */
export const getAvailableCropNames = (): string[] => {
  return Object.keys(cropImageMap);
};

/**
 * Check if a crop has an available image
 * @param cropName - The name of the crop to check
 * @returns True if the crop has an image, false otherwise
 */
export const hasCropImage = (cropName: string): boolean => {
  if (!cropName) return false;
  
  // Normalize the crop name: uppercase and replace spaces with underscores
  const normalizedName = cropName.toUpperCase().trim().replace(/\s+/g, '_');
  
  // Check exact match
  if (cropImageMap[normalizedName]) {
    return true;
  }
  
  // Check partial matches with improved logic
  for (const key of Object.keys(cropImageMap)) {
    // Check if the normalized name contains the key or vice versa
    if (normalizedName.includes(key) || key.includes(normalizedName)) {
      return true;
    }
    
    // Also try matching without underscores
    const nameWithoutUnderscores = normalizedName.replace(/_/g, '');
    const keyWithoutUnderscores = key.replace(/_/g, '');
    if (nameWithoutUnderscores.includes(keyWithoutUnderscores) || keyWithoutUnderscores.includes(nameWithoutUnderscores)) {
      return true;
    }
  }
  
  return false;
}; 