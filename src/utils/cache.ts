interface CacheItem<T> {
  data: T;
  timestamp: number;
}

export class CacheManager {
  private cache: Map<string, CacheItem<any>> = new Map();
  private defaultExpiry: number;

  constructor(defaultExpiryMs: number = 3600000) { // Default 1 hour
    this.defaultExpiry = defaultExpiryMs;
  }

  /**
   * Set an item in the cache
   * @param key Cache key
   * @param data Data to cache
   */
  set<T>(key: string, data: T): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  /**
   * Get an item from the cache if it exists and is not expired
   * @param key Cache key
   * @param expiryMs Custom expiry time in milliseconds
   * @returns The cached data or null if not found or expired
   */
  get<T>(key: string, expiryMs?: number): T | null {
    const item = this.cache.get(key);
    
    if (!item) {
      return null;
    }

    const expiry = expiryMs || this.defaultExpiry;
    const now = Date.now();
    
    if (now - item.timestamp > expiry) {
      this.cache.delete(key);
      return null;
    }

    return item.data as T;
  }

  /**
   * Check if an item exists in the cache and is not expired
   * @param key Cache key
   * @param expiryMs Custom expiry time in milliseconds
   * @returns True if the item exists and is not expired
   */
  has(key: string, expiryMs?: number): boolean {
    return this.get(key, expiryMs) !== null;
  }

  /**
   * Remove an item from the cache
   * @param key Cache key
   */
  delete(key: string): void {
    this.cache.delete(key);
  }

  /**
   * Clear all items from the cache
   */
  clear(): void {
    this.cache.clear();
  }

  /**
   * Get or set an item in the cache
   * @param key Cache key
   * @param fetchFn Function to fetch data if not in cache
   * @param expiryMs Custom expiry time in milliseconds
   * @param forceRefresh Force refresh the cache
   * @returns The cached or fetched data
   */
  async getOrSet<T>(
    key: string, 
    fetchFn: () => Promise<T>, 
    expiryMs?: number,
    forceRefresh: boolean = false
  ): Promise<T> {
    if (!forceRefresh) {
      const cachedData = this.get<T>(key, expiryMs);
      if (cachedData !== null) {
        return cachedData;
      }
    }

    const data = await fetchFn();
    this.set(key, data);
    return data;
  }
} 