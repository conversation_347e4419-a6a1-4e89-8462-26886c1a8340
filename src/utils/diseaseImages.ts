import { imagePathMap } from '../data/images';

// Placeholder image for diseases without specific images
const imagePlaceholder = require('../../../assets/img/smallImage.png');

/**
 * Get disease image by disease name
 * @param diseaseName - The name of the disease
 * @returns The image path for the disease or a placeholder if not found
 */
export const getDiseaseImage = (diseaseName: string): string | null => {
  if (!diseaseName) return null;
  
  // Normalize the disease name: lowercase and handle spaces
  const normalizedName = diseaseName.toLowerCase().trim();
  
  // Search through the image path map for disease images
  for (const [_imageId, imagePath] of Object.entries(imagePathMap)) {
    if (imagePath.includes('/DISEASES/')) {
      // Extract the filename from the path
      const filename = imagePath.split('/').pop();
      if (filename) {
        // Remove the .png extension and normalize
        const imageBaseName = filename.replace('.png', '').toLowerCase();
        
        // Check if the disease name matches the image name
        if (imageBaseName.includes(normalizedName) || normalizedName.includes(imageBaseName)) {
          return imagePath;
        }
        
        // Also try matching individual words
        const diseaseWords = normalizedName.split(/[\s-]+/);
        const imageWords = imageBaseName.split(/[\s-]+/);
        
        const hasMatchingWords = diseaseWords.some(diseaseWord => 
          imageWords.some(imageWord => 
            diseaseWord.includes(imageWord) || imageWord.includes(diseaseWord)
          )
        );
        
        if (hasMatchingWords) {
          return imagePath;
        }
      }
    }
  }
  
  return null;
};

/**
 * Get disease image by image ID
 * @param imageId - The ID of the disease image
 * @returns The image path for the disease or null if not found
 */
export const getDiseaseImageById = (imageId: string): string | null => {
  const imagePath = imagePathMap[imageId];
  
  // Only return if it's a disease image
  if (imagePath && imagePath.includes('/DISEASES/')) {
    return imagePath;
  }
  
  return null;
};

/**
 * Get disease type image by disease type name
 * @param diseaseTypeName - The name of the disease type
 * @returns The image path for the disease type or null if not found
 */
export const getDiseaseTypeImage = (diseaseTypeName: string): string | null => {
  if (!diseaseTypeName) return null;
  
  // Normalize the disease type name: lowercase
  const normalizedName = diseaseTypeName.toLowerCase().trim();
  
  // Search through the image path map for disease type images
  for (const [_imageId, imagePath] of Object.entries(imagePathMap)) {
    if (imagePath.includes('/DISEASE_TYPE/')) {
      // Extract the filename from the path
      const filename = imagePath.split('/').pop();
      if (filename) {
        // Remove the .png extension and normalize
        const imageBaseName = filename.replace('.png', '').toLowerCase();
        
        // Check if the disease type name matches the image name
        if (imageBaseName.includes(normalizedName) || normalizedName.includes(imageBaseName)) {
          return imagePath;
        }
      }
    }
  }
  
  return null;
};

/**
 * Get disease type image by image ID
 * @param imageId - The ID of the disease type image
 * @returns The image path for the disease type or null if not found
 */
export const getDiseaseTypeImageById = (imageId: string): string | null => {
  const imagePath = imagePathMap[imageId];
  
  // Only return if it's a disease type image
  if (imagePath && imagePath.includes('/DISEASE_TYPE/')) {
    return imagePath;
  }
  
  return null;
};

/**
 * Get all available disease names that have images
 * @returns Array of disease names that have corresponding images
 */
export const getAvailableDiseaseNames = (): string[] => {
  const diseaseNames: string[] = [];
  
  for (const [_imageId, imagePath] of Object.entries(imagePathMap)) {
    if (imagePath.includes('/DISEASES/')) {
      const filename = imagePath.split('/').pop();
      if (filename) {
        // Remove the .png extension and add to the list
        const diseaseName = filename.replace('.png', '');
        diseaseNames.push(diseaseName);
      }
    }
  }
  
  return diseaseNames;
};

/**
 * Get all available disease type names that have images
 * @returns Array of disease type names that have corresponding images
 */
export const getAvailableDiseaseTypeNames = (): string[] => {
  const diseaseTypeNames: string[] = [];
  
  for (const [_imageId, imagePath] of Object.entries(imagePathMap)) {
    if (imagePath.includes('/DISEASE_TYPE/')) {
      const filename = imagePath.split('/').pop();
      if (filename) {
        // Remove the .png extension and add to the list
        const diseaseTypeName = filename.replace('.png', '');
        diseaseTypeNames.push(diseaseTypeName);
      }
    }
  }
  
  return diseaseTypeNames;
};

/**
 * Check if a disease has an available image
 * @param diseaseName - The name of the disease to check
 * @returns True if the disease has an image, false otherwise
 */
export const hasDiseaseImage = (diseaseName: string): boolean => {
  return getDiseaseImage(diseaseName) !== null;
};

/**
 * Check if a disease type has an available image
 * @param diseaseTypeName - The name of the disease type to check
 * @returns True if the disease type has an image, false otherwise
 */
export const hasDiseaseTypeImage = (diseaseTypeName: string): boolean => {
  return getDiseaseTypeImage(diseaseTypeName) !== null;
};

/**
 * Get all disease image IDs
 * @returns Array of image IDs for diseases
 */
export const getAllDiseaseImageIds = (): string[] => {
  const diseaseImageIds: string[] = [];
  
  for (const [imageId, imagePath] of Object.entries(imagePathMap)) {
    if (imagePath.includes('/DISEASES/')) {
      diseaseImageIds.push(imageId);
    }
  }
  
  return diseaseImageIds;
};

/**
 * Get all disease type image IDs
 * @returns Array of image IDs for disease types
 */
export const getAllDiseaseTypeImageIds = (): string[] => {
  const diseaseTypeImageIds: string[] = [];
  
  for (const [imageId, imagePath] of Object.entries(imagePathMap)) {
    if (imagePath.includes('/DISEASE_TYPE/')) {
      diseaseTypeImageIds.push(imageId);
    }
  }
  
  return diseaseTypeImageIds;
};
