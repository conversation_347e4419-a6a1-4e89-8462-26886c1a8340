import { ImageData, ObjectId } from '../types';
import { getImageUrl as getLocalImageUrl } from '../data/images';

/**
 * Image utility functions for the Crop Library SDK
 */
export class ImageUtils {
  private basePath: string;
  
  /**
   * Create a new ImageUtils instance
   * @param basePath Base path for images (optional)
   */
  constructor(basePath: string = '') {
    this.basePath = basePath;
  }
  
  /**
   * Get the full URL for an image by its ID
   * @param imageId Image ID object
   * @param size Image size (ignored in local implementation)
   * @returns Full image URL
   */
  getImageUrl(imageId: ObjectId | undefined, size: 'thumbnail' | 'small' | 'medium' | 'large' | 'original' = 'medium'): string | null {
    if (!imageId) {
      return null;
    }
    
    const imagePath = getLocalImageUrl(imageId.$oid);
    
    // If no image path found, return null
    if (!imagePath) {
      return null;
    }
    
    // Prepend the base path if provided
    return this.basePath ? `${this.basePath}/${imagePath}` : imagePath;
  }
  
  /**
   * Get the full URL for a thumbnail image by its ID
   * @param imageId Image ID object
   * @returns Thumbnail image URL
   */
  getThumbnailUrl(imageId: ObjectId | undefined): string | null {
    return this.getImageUrl(imageId);
  }
  
  /**
   * Convert an image ID to an ImageData object with URLs
   * @param imageId Image ID object
   * @returns ImageData object with URLs
   */
  createImageData(imageId: ObjectId | undefined): ImageData | null {
    if (!imageId) {
      return null;
    }
    
    const imageUrl = this.getImageUrl(imageId);
    if (!imageUrl) {
      return null;
    }
    
    return {
      _id: imageId,
      url: imageUrl,
      thumbnailUrl: imageUrl
    };
  }
} 