// Export the main CropLibrary class
export { CropLibrary } from './core/CropLibrary';

// Export types
export {
  AgriItem,
  AgriItemType,
  AgriPlant,
  CropFamily,
  CropLibraryConfig,
  CropLibraryOptions,
  DateType,
  ImageData,
  LangMapItem,
  LangText,
  MeasurementUnit,
  ObjectId,
  SummarySection
} from './types';

// Export utility classes
export { CacheManager } from './utils/cache';
export { ImageUtils } from './utils/images';

// Export crop image utilities
export {
  getCropImage,
  getAvailableCropNames,
  hasCropImage
} from './utils/cropImages';

// Export raw data for direct access
export {
  cropFamilies,
  getAllCropFamilies,
  getCropFamilyById
} from './data/cropFamilies';

export {
  plants,
  getAllPlants,
  getPlantById,
  getPlantsByCropFamily,
  searchPlants
} from './data/plants';

export {
  imagePathMap,
  getImageById,
  getImageUrl,
  getAllImageIds
} from './data/images'; 