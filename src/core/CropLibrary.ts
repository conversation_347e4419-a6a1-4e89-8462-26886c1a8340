import { 
  AgriPlant, 
  CropFamily, 
  CropLibraryConfig, 
  CropLibraryOptions, 
  ImageData,
  ObjectId
} from '../types';
import { CacheManager } from '../utils/cache';
import { ImageUtils } from '../utils/images';
import { 
  getAllCropFamilies,
  getCropFamilyById
} from '../data/cropFamilies';
import {
  getAllPlants,
  getPlantById,
  getPlantsByCropFamily,
  searchPlants as searchPlantsData
} from '../data/plants';
import {
  getImageById as getImageDataById,
  getImageUrl as getImageDataUrl
} from '../data/images';

/**
 * Main class for the Crop Library SDK
 */
export class CropLibrary {
  private cache: CacheManager;
  private imageUtils: ImageUtils;
  private defaultLanguage: string;
  private basePath: string;

  /**
   * Create a new CropLibrary instance
   * @param config Configuration options
   */
  constructor(config?: Partial<CropLibraryConfig>) {
    this.cache = new CacheManager(config?.cacheExpiry || 3600000); // Default 1 hour
    this.basePath = config?.basePath || '';
    this.imageUtils = new ImageUtils(this.basePath);
    this.defaultLanguage = config?.language || 'en';
  }

  /**
   * Get all crop families
   * @param options Request options
   * @returns Array of crop families
   */
  async getCropFamilies(options?: CropLibraryOptions): Promise<CropFamily[]> {
    const language = options?.language || this.defaultLanguage;
    const cacheKey = `crop-families-${language}`;
    
    return this.cache.getOrSet<CropFamily[]>(
      cacheKey,
      async () => getAllCropFamilies(language),
      undefined,
      options?.forceRefresh
    );
  }

  /**
   * Get a crop family by ID
   * @param id Crop family ID
   * @param options Request options
   * @returns Crop family or null if not found
   */
  async getCropFamilyById(id: string, options?: CropLibraryOptions): Promise<CropFamily | null> {
    const cacheKey = `crop-family-${id}`;
    
    return this.cache.getOrSet<CropFamily | null>(
      cacheKey,
      async () => getCropFamilyById(id),
      undefined,
      options?.forceRefresh
    );
  }

  /**
   * Get all plants
   * @param options Request options
   * @returns Array of plants
   */
  async getPlants(options?: CropLibraryOptions): Promise<AgriPlant[]> {
    const language = options?.language || this.defaultLanguage;
    const cacheKey = `plants-${language}`;
    
    return this.cache.getOrSet<AgriPlant[]>(
      cacheKey,
      async () => getAllPlants(language),
      undefined,
      options?.forceRefresh
    );
  }

  /**
   * Get a plant by ID
   * @param id Plant ID
   * @param options Request options
   * @returns Plant or null if not found
   */
  async getPlantById(id: string, options?: CropLibraryOptions): Promise<AgriPlant | null> {
    const cacheKey = `plant-${id}`;
    
    return this.cache.getOrSet<AgriPlant | null>(
      cacheKey,
      async () => getPlantById(id),
      undefined,
      options?.forceRefresh
    );
  }

  /**
   * Get plants by crop family ID
   * @param cropFamilyId Crop family ID
   * @param options Request options
   * @returns Array of plants
   */
  async getPlantsByCropFamily(cropFamilyId: string, options?: CropLibraryOptions): Promise<AgriPlant[]> {
    const language = options?.language || this.defaultLanguage;
    const cacheKey = `plants-by-family-${cropFamilyId}-${language}`;
    
    return this.cache.getOrSet<AgriPlant[]>(
      cacheKey,
      async () => {
        const plants = getPlantsByCropFamily(cropFamilyId);
        if (language === 'en') return plants;
        
        // Filter by language
        return plants.filter(plant => 
          plant.langMap.some(lang => lang.langCode === language)
        );
      },
      undefined,
      options?.forceRefresh
    );
  }

  /**
   * Search for plants by name
   * @param query Search query
   * @param options Request options
   * @returns Array of matching plants
   */
  async searchPlants(query: string, options?: CropLibraryOptions): Promise<AgriPlant[]> {
    const language = options?.language || this.defaultLanguage;
    const cacheKey = `plants-search-${query}-${language}`;
    
    return this.cache.getOrSet<AgriPlant[]>(
      cacheKey,
      async () => searchPlantsData(query, language),
      undefined,
      options?.forceRefresh
    );
  }

  /**
   * Get an image by ID
   * @param imageId Image ID
   * @param options Request options
   * @returns Image data or null if not found
   */
  async getImageById(imageId: string, options?: CropLibraryOptions): Promise<ImageData | null> {
    const cacheKey = `image-${imageId}`;
    
    return this.cache.getOrSet<ImageData | null>(
      cacheKey,
      async () => getImageDataById(imageId),
      undefined,
      options?.forceRefresh
    );
  }

  /**
   * Get image URL by ID
   * @param imageId Image ID object or string
   * @param size Image size (ignored in local implementation)
   * @returns Image URL or null if ID is undefined
   */
  getImageUrl(imageId: ObjectId | string | undefined, size?: 'thumbnail' | 'small' | 'medium' | 'large' | 'original'): string | null {
    if (!imageId) {
      return null;
    }
    
    // Handle both ObjectId and string formats
    const id = typeof imageId === 'string' ? imageId : imageId.$oid;
    const imagePath = getImageDataUrl(id);
    
    // If no image path found, return null
    if (!imagePath) {
      return null;
    }
    
    // Prepend the base path if provided
    return this.basePath ? `${this.basePath}/${imagePath}` : imagePath;
  }

  /**
   * Clear the cache
   */
  clearCache(): void {
    this.cache.clear();
  }
}