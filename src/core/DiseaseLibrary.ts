import {
  Disease,
  DiseaseType,
  DiseaseLibraryConfig,
  DiseaseLibraryOptions,
  ImageData,
  ObjectId
} from '../types';
import { CacheManager } from '../utils/cache';
import {
  getAllDiseaseTypes,
  getDiseaseTypeById
} from '../data/diseaseTypes';
import {
  getAllDiseases,
  getDiseaseById,
  getDiseasesByType,
  searchDiseases as searchDiseasesData
} from '../data/diseases';
import {
  getImageById as getImageDataById,
  getImageUrl as getImageDataUrl
} from '../data/images';

/**
 * Main class for the Disease Library SDK
 */
export class DiseaseLibrary {
  private cache: CacheManager;
  private defaultLanguage: string;
  private basePath: string;

  /**
   * Create a new DiseaseLibrary instance
   * @param config Configuration options
   */
  constructor(config?: Partial<DiseaseLibraryConfig>) {
    this.cache = new CacheManager(config?.cacheExpiry || 3600000); // Default 1 hour
    this.basePath = config?.basePath || '';
    this.defaultLanguage = config?.language || 'en';
  }

  /**
   * Get all disease types
   * @param options Request options
   * @returns Array of disease types
   */
  async getDiseaseTypes(options?: DiseaseLibraryOptions): Promise<DiseaseType[]> {
    const language = options?.language || this.defaultLanguage;
    const cacheKey = `disease-types-${language}`;

    return this.cache.getOrSet<DiseaseType[]>(
      cacheKey,
      async () => getAllDiseaseTypes(language),
      undefined,
      options?.forceRefresh
    );
  }

  /**
   * Get a disease type by ID
   * @param id Disease type ID
   * @param options Request options
   * @returns Disease type or null if not found
   */
  async getDiseaseTypeById(id: string, options?: DiseaseLibraryOptions): Promise<DiseaseType | null> {
    const cacheKey = `disease-type-${id}`;

    return this.cache.getOrSet<DiseaseType | null>(
      cacheKey,
      async () => getDiseaseTypeById(id),
      undefined,
      options?.forceRefresh
    );
  }

  /**
   * Get all diseases
   * @param options Request options
   * @returns Array of diseases
   */
  async getDiseases(options?: DiseaseLibraryOptions): Promise<Disease[]> {
    const language = options?.language || this.defaultLanguage;
    const cacheKey = `diseases-${language}`;

    return this.cache.getOrSet<Disease[]>(
      cacheKey,
      async () => getAllDiseases(language),
      undefined,
      options?.forceRefresh
    );
  }

  /**
   * Get a disease by ID
   * @param id Disease ID
   * @param options Request options
   * @returns Disease or null if not found
   */
  async getDiseaseById(id: string, options?: DiseaseLibraryOptions): Promise<Disease | null> {
    const cacheKey = `disease-${id}`;

    return this.cache.getOrSet<Disease | null>(
      cacheKey,
      async () => getDiseaseById(id),
      undefined,
      options?.forceRefresh
    );
  }

  /**
   * Get diseases by disease type ID
   * @param diseaseTypeId Disease type ID
   * @param options Request options
   * @returns Array of diseases
   */
  async getDiseasesByType(diseaseTypeId: string, options?: DiseaseLibraryOptions): Promise<Disease[]> {
    const language = options?.language || this.defaultLanguage;
    const cacheKey = `diseases-by-type-${diseaseTypeId}-${language}`;

    return this.cache.getOrSet<Disease[]>(
      cacheKey,
      async () => {
        const diseases = getDiseasesByType(diseaseTypeId);
        if (language === 'en') return diseases;

        // Filter by language
        return diseases.filter(disease =>
          disease.langMap.some(lang => lang.langCode === language)
        );
      },
      undefined,
      options?.forceRefresh
    );
  }

  /**
   * Search for diseases by name
   * @param query Search query
   * @param options Request options
   * @returns Array of matching diseases
   */
  async searchDiseases(query: string, options?: DiseaseLibraryOptions): Promise<Disease[]> {
    const language = options?.language || this.defaultLanguage;
    const cacheKey = `diseases-search-${query}-${language}`;

    return this.cache.getOrSet<Disease[]>(
      cacheKey,
      async () => searchDiseasesData(query, language),
      undefined,
      options?.forceRefresh
    );
  }

  /**
   * Get an image by ID
   * @param imageId Image ID
   * @param options Request options
   * @returns Image data or null if not found
   */
  async getImageById(imageId: string, options?: DiseaseLibraryOptions): Promise<ImageData | null> {
    const cacheKey = `image-${imageId}`;

    return this.cache.getOrSet<ImageData | null>(
      cacheKey,
      async () => getImageDataById(imageId),
      undefined,
      options?.forceRefresh
    );
  }

  /**
   * Get image URL by ID
   * @param imageId Image ID object or string
   * @param size Image size (ignored in local implementation)
   * @returns Image URL or null if ID is undefined
   */
  getImageUrl(imageId: ObjectId | string | undefined, _size?: 'thumbnail' | 'small' | 'medium' | 'large' | 'original'): string | null {
    if (!imageId) {
      return null;
    }

    // Handle both ObjectId and string formats
    const id = typeof imageId === 'string' ? imageId : imageId.$oid;
    const imagePath = getImageDataUrl(id);

    // If no image path found, return null
    if (!imagePath) {
      return null;
    }

    // Prepend the base path if provided
    return this.basePath ? `${this.basePath}/${imagePath}` : imagePath;
  }

  /**
   * Clear the cache
   */
  clearCache(): void {
    this.cache.clear();
  }
}