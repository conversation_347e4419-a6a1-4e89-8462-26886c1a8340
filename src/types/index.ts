export type LangText = {
  langCode: string;
  text: string;
};

export type ObjectId = {
  $oid: string;
};

export type DateType = {
  $date: string;
};

export type CropFamily = {
  _id: ObjectId;
  name: LangText[];
  description: LangText[];
  rotationAfter: ObjectId[];
  rotationsBeforeRepeat: number;
  createdAt: DateType;
  updatedAt: DateType;
  __v: number;
  imageId?: ObjectId;
};

export type MeasurementUnit = {
  value: number | null;
  unit: string;
};

export type AgriItemType = 'GENERAL' | 'NATURAL' | 'CHEMICAL';

export type AgriItem = {
  agriItemType: AgriItemType;
  displayOrder: number;
  text: string | null;
  imageId?: ObjectId;
};

export type SummarySection = {
  summary: string | null;
};

export type LangMapItem = {
  langCode: string;
  name: string;
  summary: string;
  optimalPlantingCondition: AgriItem[];
  soil: SummarySection;
  soilAndLandPreparation: AgriItem[];
  planting: AgriItem[];
  growing: AgriItem[];
  weeding: AgriItem[];
  irrigation: AgriItem[];
  fertilizer: AgriItem[];
  harvest: AgriItem[];
  postHarvest: AgriItem[];
  interCropping: any[];
  pest: SummarySection;
  diesease: SummarySection;
  generalTips: string | null;
};

export type AgriPlant = {
  _id: ObjectId;
  cropFamily: ObjectId;
  expectedYieldFallBack: {
    unit: string;
  };
  langMap: LangMapItem[];
  imageIds: any[];
  diseaseIds: any[];
  pestIds: (ObjectId | null)[];
  optimalSoilTypesIds: (ObjectId | null)[];
  createdAt: DateType;
  updatedAt: DateType;
  __v: number;
  expectedYield: Record<string, any>;
  profileImageId?: ObjectId;
  suitability: Record<string, any>;
  daysToFirstShoot: MeasurementUnit;
  daysToHarvest: MeasurementUnit;
  daysToMaturity: MeasurementUnit;
  plantSpacing: MeasurementUnit;
  rowSpacing: MeasurementUnit;
  pruningInfo?: string;
};

export type ImageData = {
  _id: ObjectId;
  url: string;
  thumbnailUrl?: string;
  metadata?: Record<string, any>;
};

export interface CropLibraryConfig {
  baseUrl?: string;
  basePath?: string; // Path to the images directory
  cacheExpiry?: number; // Time in milliseconds
  language?: string;
}

export interface CropLibraryOptions {
  language?: string;
  forceRefresh?: boolean;
}
