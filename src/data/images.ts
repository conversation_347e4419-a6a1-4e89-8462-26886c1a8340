import { ImageData } from '../types';
import imageLibrariesData from './json/farm_stack.imagelibraries.json';

/**
 * Map of image IDs to their file paths
 */
export const imagePathMap: Record<string, string> = {};

// Process image library data to create a mapping of image IDs to file paths
(imageLibrariesData as any[]).forEach(image => {
  if (image._id && image._id.$oid && image.Key) {
    // Extract the filename from the key
    let filename = image.Key.split('/').pop();
    
    if (filename) {
      // Check if there's a PNG version in altType
      if (image.altType && image.altType.png && image.altType.png.key) {
        filename = image.altType.png.key.split('/').pop();
        console.log(`Using PNG altType for ${image._id.$oid}: ${filename}`);
      } else {
        // Convert SVG to PNG for React Native compatibility
        if (filename.endsWith('.svg')) {
          filename = filename.replace('.svg', '.png');
          console.log(`Converted SVG to PNG for ${image._id.$oid}: ${filename}`);
        } else if (!filename.endsWith('.png')) {
          // If it doesn't have an extension, assume it's PNG
          filename = filename + '.png';
          console.log(`Added PNG extension for ${image._id.$oid}: ${filename}`);
        }
      }

      // Clean up the filename to match the actual files in the PNG directory
      let cleanFilename = filename;
      
      // Remove common suffixes and prefixes
      cleanFilename = cleanFilename
        .replace(/ colour ground.*\.png$/i, '.png')
        .replace(/ coloured ground.*\.png$/i, '.png')
        .replace(/ small.*\.png$/i, '.png')
        .replace(/ ldpi.*\.png$/i, '.png')
        .replace(/ xxhdpi.*\.png$/i, '.png')
        .replace(/ smalldpi.*\.png$/i, '.png')
        .replace(/ small\.aildpi.*\.png$/i, '.png')
        .replace(/ smallxxhdpi.*\.png$/i, '.png')
        .replace(/\.png\.png$/i, '.png'); // Fix double .png extension
      
      // Convert spaces to underscores to match actual filenames
      cleanFilename = cleanFilename.replace(/\s+/g, '_');
      
      // Handle special cases
      if (cleanFilename.toLowerCase().includes('unlisted_crop')) {
        cleanFilename = 'unlisted crop.png';
      }
      
      // Handle specific crop name mappings
      const cropNameMappings: Record<string, string> = {
        'Peanut.png': 'PEANUT.png',
        'corn.png': 'CORN.png',
        'GLIRICIDIA_TREE.png': 'GLIRICIDIA.png',
        'KOLA_NUT.png': 'KOLA_NUT.png',
        'KOHLRABI.png': 'KOHLRABI.png',
        'KOHL_RABI.png': 'KOHLRABI.png',
        'LABLAB_BEAN.png': 'LABLAB_BEAN.png',
        'RED_CLOVER.png': 'RED_CLOVER.png',
        'KIDNEY_BEAN.png': 'KIDNEY_BEAN.png',
        'RASPBERRY_colored_ground.png': 'RASPBERRY colored ground smallxxhdpi.png',
      };
      
      if (cropNameMappings[cleanFilename]) {
        cleanFilename = cropNameMappings[cleanFilename];
      }
      
      // Map the image ID to its path (relative path only, base path will be prepended by SDK)
      const imagePath = `images/AGRI_DATA/AGRI_PLANTS/PNG/${cleanFilename}`;
      imagePathMap[image._id.$oid] = imagePath;
      console.log(`Mapped image ${image._id.$oid} to: ${imagePath}`);
    }
  }
});

/**
 * Get image data by ID
 * @param id Image ID
 * @returns Image data or null if not found
 */
export function getImageById(id: string): ImageData | null {
  const imagePath = imagePathMap[id];
  
  if (!imagePath) {
    return null;
  }
  
  try {
    // In a browser environment, we would return a URL to the image
    // In a Node.js environment, we could read the file and return its contents
    return {
      _id: { $oid: id },
      url: imagePath,
      thumbnailUrl: imagePath
    };
  } catch (error) {
    console.error(`Error loading image with ID ${id}:`, error);
    return null;
  }
}

/**
 * Get image URL by ID
 * @param id Image ID
 * @returns Image URL or null if not found
 */
export function getImageUrl(id: string): string | null {
  return imagePathMap[id] || null;
}

/**
 * Get all available image IDs
 * @returns Array of image IDs
 */
export function getAllImageIds(): string[] {
  return Object.keys(imagePathMap);
} 