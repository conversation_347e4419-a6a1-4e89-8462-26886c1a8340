import { DiseaseType } from '../types';
import diseaseTypesData from './json/farm_stack.agridiseasetypes.json';

/**
 * Local database of disease types loaded from JSON file
 */
export const diseaseTypes: DiseaseType[] = diseaseTypesData as unknown as DiseaseType[];

/**
 * Get all disease types
 * @param language Language code (default: 'en')
 * @returns Array of disease types
 */
export function getAllDiseaseTypes(language: string = 'en'): DiseaseType[] {
  if (language === 'en') {
    return diseaseTypes;
  }
  
  // Filter disease types by language
  return diseaseTypes.filter(diseaseType => 
    diseaseType.name.some(n => n.langCode === language) ||
    diseaseType.description.some(d => d.langCode === language)
  );
}

/**
 * Get a disease type by ID
 * @param id Disease type ID
 * @returns Disease type or null if not found
 */
export function getDiseaseTypeById(id: string): DiseaseType | null {
  return diseaseTypes.find(diseaseType => diseaseType._id.$oid === id) || null;
}

/**
 * Search disease types by name
 * @param query Search query
 * @param language Language code (default: 'en')
 * @returns Array of matching disease types
 */
export function searchDiseaseTypes(query: string, language: string = 'en'): DiseaseType[] {
  const normalizedQuery = query.toLowerCase();
  
  return diseaseTypes.filter(diseaseType => {
    const nameItem = diseaseType.name.find(n => n.langCode === language);
    const descriptionItem = diseaseType.description.find(d => d.langCode === language);
    
    if (!nameItem) return false;
    
    return nameItem.text.toLowerCase().includes(normalizedQuery) || 
           (descriptionItem && descriptionItem.text.toLowerCase().includes(normalizedQuery));
  });
}
