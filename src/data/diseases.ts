import { Disease } from '../types';
import diseasesData from './json/farm_stack.agridiseases.json';

/**
 * Local database of diseases loaded from JSON file
 */
export const diseases: Disease[] = diseasesData as unknown as Disease[];

/**
 * Get all diseases
 * @param language Language code (default: 'en')
 * @returns Array of diseases
 */
export function getAllDiseases(language: string = 'en'): Disease[] {
  if (language === 'en') {
    return diseases;
  }

  // Filter diseases by language
  return diseases.filter(disease =>
    disease.langMap.some(lang => lang.langCode === language)
  );
}

/**
 * Get a disease by ID
 * @param id Disease ID
 * @returns Disease or null if not found
 */
export function getDiseaseById(id: string): Disease | null {
  return diseases.find(disease => disease._id.$oid === id) || null;
}

/**
 * Get diseases by disease type ID
 * @param diseaseTypeId Disease type ID
 * @returns Array of diseases of the specified type
 */
export function getDiseasesByType(diseaseTypeId: string): Disease[] {
  return diseases.filter(disease => disease.diseaseTypeId.$oid === diseaseTypeId);
}

/**
 * Get diseases by disease type ID with language filtering
 * @param diseaseTypeId Disease type ID
 * @param language Language code (default: 'en')
 * @returns Array of diseases of the specified type in the specified language
 */
export function getDiseasesByTypeAndLanguage(diseaseTypeId: string, language: string = 'en'): Disease[] {
  const diseasesByType = getDiseasesByType(diseaseTypeId);

  if (language === 'en') {
    return diseasesByType;
  }

  // Filter by language
  return diseasesByType.filter(disease =>
    disease.langMap.some(lang => lang.langCode === language)
  );
}

/**
 * Search diseases by name
 * @param query Search query
 * @param language Language code (default: 'en')
 * @returns Array of matching diseases
 */
export function searchDiseases(query: string, language: string = 'en'): Disease[] {
  const normalizedQuery = query.toLowerCase();

  return diseases.filter(disease => {
    const langMapItem = disease.langMap.find(lang => lang.langCode === language);
    if (!langMapItem) return false;

    return langMapItem.name.toLowerCase().includes(normalizedQuery);
  });
}