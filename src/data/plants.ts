import { AgriPlant } from '../types';
import agriPlantsData from './json/farm_stack.agriplants.json';

/**
 * Local database of plants loaded from JSON file
 */
export const plants: AgriPlant[] = agriPlantsData as unknown as AgriPlant[];

/**
 * Get all plants
 * @param language Language code (default: 'en')
 * @returns Array of plants
 */
export function getAllPlants(language: string = 'en'): AgriPlant[] {
  if (language === 'en') {
    return plants;
  }
  
  // Filter plants by language
  return plants.filter(plant => 
    plant.langMap.some(lang => lang.langCode === language)
  );
}

/**
 * Get a plant by ID
 * @param id Plant ID
 * @returns Plant or null if not found
 */
export function getPlantById(id: string): AgriPlant | null {
  return plants.find(plant => plant._id.$oid === id) || null;
}

/**
 * Get plants by crop family ID
 * @param cropFamilyId Crop family ID
 * @returns Array of plants in the specified crop family
 */
export function getPlantsByCropFamily(cropFamilyId: string): AgriPlant[] {
  return plants.filter(plant => plant.cropFamily.$oid === cropFamilyId);
}

/**
 * Search plants by name
 * @param query Search query
 * @param language Language code (default: 'en')
 * @returns Array of matching plants
 */
export function searchPlants(query: string, language: string = 'en'): AgriPlant[] {
  const normalizedQuery = query.toLowerCase();
  
  return plants.filter(plant => {
    const langMapItem = plant.langMap.find(lang => lang.langCode === language);
    if (!langMapItem) return false;
    
    return langMapItem.name.toLowerCase().includes(normalizedQuery) || 
           (langMapItem.summary && langMapItem.summary.toLowerCase().includes(normalizedQuery));
  });
} 