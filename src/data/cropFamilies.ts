import { CropFamily } from '../types';
import cropFamiliesData from './json/farm_stack.agricropfamilies.json';

/**
 * Local database of crop families loaded from JSON file
 */
export const cropFamilies: CropFamily[] = cropFamiliesData as unknown as CropFamily[];

/**
 * Get all crop families
 * @param language Language code (default: 'en')
 * @returns Array of crop families
 */
export function getAllCropFamilies(language: string = 'en'): CropFamily[] {
  if (language === 'en') {
    return cropFamilies;
  }
  
  // Filter crop families by language
  return cropFamilies.filter(family => 
    family.name.some(n => n.langCode === language) ||
    family.description.some(d => d.langCode === language)
  );
}

/**
 * Get a crop family by ID
 * @param id Crop family ID
 * @returns Crop family or null if not found
 */
export function getCropFamilyById(id: string): CropFamily | null {
  return cropFamilies.find(family => family._id.$oid === id) || null;
} 