# Crop Library SDK

A simple TypeScript library for accessing crop family and plant information with image management capabilities.

## Features

- Access crop family information
- Access plant information with detailed growing instructions
- Image management for crop-related images
- Built-in caching for performance
- TypeScript support
- Works in both browser and Node.js environments
- Fully offline capable - all data embedded in the project

## Usage

Simply import and use the library in your TypeScript project:

```typescript
import { CropLibrary } from './src';

// Initialize the SDK
const cropLibrary = new CropLibrary({
  language: 'en',
  basePath: './src/data/images' // Path to the images directory
});

// Get all crop families
const cropFamilies = await cropLibrary.getCropFamilies();

// Get plants by crop family
const plants = await cropLibrary.getPlantsByCropFamily(cropFamilyId);

// Search for plants
const searchResults = await cropLibrary.searchPlants('tomato');

// Get image URL
const imageUrl = cropLibrary.getImageUrl(imageId);
```

Check the examples directory for more detailed usage examples.