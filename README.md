# Crop Library SDK

A comprehensive TypeScript library for accessing crop family, plant, and disease information with image management capabilities.

## Features

- Access crop family information
- Access plant information with detailed growing instructions
- Access disease and disease type information
- Image management for crop and disease-related images
- Built-in caching for performance
- TypeScript support
- Works in both browser and Node.js environments
- Fully offline capable - all data embedded in the project

## Usage

### CropLibrary

Import and use the CropLibrary for accessing crop and plant information:

```typescript
import { CropLibrary } from './src';

// Initialize the SDK
const cropLibrary = new CropLibrary({
  language: 'en',
  basePath: './src/data/images' // Path to the images directory
});

// Get all crop families
const cropFamilies = await cropLibrary.getCropFamilies();

// Get plants by crop family
const plants = await cropLibrary.getPlantsByCropFamily(cropFamilyId);

// Search for plants
const searchResults = await cropLibrary.searchPlants('tomato');

// Get image URL
const imageUrl = cropLibrary.getImageUrl(imageId);
```

### DiseaseLibrary

Import and use the DiseaseLibrary for accessing disease information:

```typescript
import { DiseaseLibrary } from './src';

// Initialize the Disease Library SDK
const diseaseLibrary = new DiseaseLibrary({
  language: 'en',
  basePath: './src/data/images' // Path to the images directory
});

// Get all disease types
const diseaseTypes = await diseaseLibrary.getDiseaseTypes();

// Get all diseases
const diseases = await diseaseLibrary.getDiseases();

// Get diseases by type
const diseasesByType = await diseaseLibrary.getDiseasesByType(diseaseTypeId);

// Search for diseases
const searchResults = await diseaseLibrary.searchDiseases('blight');

// Get disease image URL
const imageUrl = diseaseLibrary.getImageUrl(disease.profileImageId);
```

Check the examples directory for more detailed usage examples.